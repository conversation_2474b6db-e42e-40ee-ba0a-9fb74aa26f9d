/**
 * Task management functionality
 * Reason for Function: Handles task creation, status updates, and task list management
 * Task Performed: Manages task CRUD operations, modal interactions, and task status transitions
 * Linking Information:
 *   - Internal Link: Uses SupabaseDB from src/renderer/js/supabase-client.js
 *   - Internal Link: Interacts with task elements in src/renderer/pages/tasks.html
 *   - Internal Link: Updates task counts and displays in task columns
 */

class TaskManager {
  constructor() {
    this.tasks = [];
    this.subTaskCounter = 1;
    this.init();
  }

  /**
   * Initialize task manager
   * Reason for Function: Sets up event listeners and loads existing tasks
   * Task Performed: Binds modal events, form handlers, and loads task data
   * Linking Information:
   *   - Internal Link: Called when tasks page is loaded
   */
  init() {
    this.bindEvents();
    this.loadTasks();
  }

  /**
   * Bind task-related events
   * Reason for Function: Attaches event listeners to task management elements
   * Task Performed: Sets up modal, form, and task interaction handlers
   * Linking Information:
   *   - Internal Link: Binds to elements in tasks.html
   */
  bindEvents() {
    // Add task button
    const addTaskBtn = document.getElementById('add-task-btn');
    if (addTaskBtn) {
      addTaskBtn.addEventListener('click', () => this.openAddTaskModal());
    }

    // Modal close buttons
    const closeModal = document.getElementById('close-task-modal');
    const cancelTask = document.getElementById('cancel-task');
    const modalOverlay = document.querySelector('.modal-overlay');

    if (closeModal) {
      closeModal.addEventListener('click', () => this.closeAddTaskModal());
    }
    if (cancelTask) {
      cancelTask.addEventListener('click', () => this.closeAddTaskModal());
    }
    if (modalOverlay) {
      modalOverlay.addEventListener('click', () => this.closeAddTaskModal());
    }

    // Add sub task button
    const addSubTaskBtn = document.getElementById('add-sub-task');
    if (addSubTaskBtn) {
      addSubTaskBtn.addEventListener('click', () => this.addSubTaskField());
    }

    // Form submission
    const addTaskForm = document.getElementById('add-task-form');
    if (addTaskForm) {
      addTaskForm.addEventListener('submit', (e) => this.handleTaskSubmission(e));
    }
  }

  /**
   * Open add task modal
   * Reason for Function: Shows the task creation modal and resets form
   * Task Performed: Displays modal, clears form, resets sub-task fields
   * Linking Information:
   *   - Internal Link: Called by add task button click
   */
  openAddTaskModal() {
    const modal = document.getElementById('add-task-modal');
    if (modal) {
      modal.classList.remove('hidden');
      this.resetForm();
      // Focus on main task title
      const mainTaskTitle = document.getElementById('main-task-title');
      if (mainTaskTitle) {
        setTimeout(() => mainTaskTitle.focus(), 100);
      }
    }
  }

  /**
   * Close add task modal
   * Reason for Function: Hides the task creation modal
   * Task Performed: Adds hidden class to modal
   * Linking Information:
   *   - Internal Link: Called by close buttons and overlay click
   */
  closeAddTaskModal() {
    const modal = document.getElementById('add-task-modal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }

  /**
   * Reset task form
   * Reason for Function: Clears all form fields and resets to default state
   * Task Performed: Clears inputs, resets sub-tasks to single field
   * Linking Information:
   *   - Internal Link: Called when opening modal
   */
  resetForm() {
    const form = document.getElementById('add-task-form');
    if (form) {
      form.reset();
    }

    // Reset sub-tasks to single field
    const container = document.getElementById('sub-tasks-container');
    if (container) {
      container.innerHTML = `
        <div class="sub-task-item">
          <input type="text" class="sub-task-input" placeholder="Enter sub task" required>
          <button type="button" class="remove-sub-task" disabled>🗑️</button>
        </div>
      `;
      this.subTaskCounter = 1;
      this.bindSubTaskEvents();
    }
  }

  /**
   * Add sub-task field
   * Reason for Function: Adds a new sub-task input field to the form
   * Task Performed: Creates new sub-task input with remove button
   * Linking Information:
   *   - Internal Link: Called by add sub-task button
   */
  addSubTaskField() {
    const container = document.getElementById('sub-tasks-container');
    if (container) {
      this.subTaskCounter++;
      const subTaskItem = document.createElement('div');
      subTaskItem.className = 'sub-task-item';
      subTaskItem.innerHTML = `
        <input type="text" class="sub-task-input" placeholder="Enter sub task">
        <button type="button" class="remove-sub-task">🗑️</button>
      `;
      container.appendChild(subTaskItem);
      this.bindSubTaskEvents();
    }
  }

  /**
   * Bind sub-task events
   * Reason for Function: Attaches event listeners to sub-task remove buttons
   * Task Performed: Sets up remove functionality for sub-task fields
   * Linking Information:
   *   - Internal Link: Called after adding sub-task fields
   */
  bindSubTaskEvents() {
    const removeButtons = document.querySelectorAll('.remove-sub-task');
    removeButtons.forEach((btn, index) => {
      btn.onclick = () => {
        if (index === 0) return; // Can't remove first sub-task
        btn.parentElement.remove();
        this.updateRemoveButtonStates();
      };
    });
    this.updateRemoveButtonStates();
  }

  /**
   * Update remove button states
   * Reason for Function: Enables/disables remove buttons based on sub-task count
   * Task Performed: Disables first remove button, enables others
   * Linking Information:
   *   - Internal Link: Called after sub-task modifications
   */
  updateRemoveButtonStates() {
    const removeButtons = document.querySelectorAll('.remove-sub-task');
    removeButtons.forEach((btn, index) => {
      btn.disabled = index === 0;
    });
  }

  /**
   * Handle task form submission
   * Reason for Function: Processes task creation form and saves to database
   * Task Performed: Validates form, creates task object, saves to database
   * Linking Information:
   *   - Internal Link: Uses SupabaseDB for database operations
   */
  async handleTaskSubmission(event) {
    event.preventDefault();
    
    const mainTitle = document.getElementById('main-task-title').value.trim();
    const priority = document.getElementById('task-priority').value;
    const dueDate = document.getElementById('task-due-date').value;
    
    // Get sub-tasks
    const subTaskInputs = document.querySelectorAll('.sub-task-input');
    const subTasks = Array.from(subTaskInputs)
      .map(input => input.value.trim())
      .filter(value => value.length > 0);

    if (!mainTitle || subTasks.length === 0) {
      alert('Please fill in the main task title and at least one sub-task.');
      return;
    }

    try {
      const createBtn = document.getElementById('create-task');
      this.setLoading(createBtn, true);

      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Create main task with sub-tasks
      const taskData = {
        title: mainTitle,
        description: `Sub-tasks: ${subTasks.join(', ')}`,
        status: 'new',
        priority: priority,
        due_date: dueDate || null,
        user_id: currentUser.id,
        sub_tasks: subTasks,
        completed_sub_tasks: []
      };

      await this.createTask(taskData);
      this.closeAddTaskModal();
      this.loadTasks();
      
    } catch (error) {
      console.error('Error creating task:', error);
      alert('Failed to create task. Please try again.');
    } finally {
      const createBtn = document.getElementById('create-task');
      this.setLoading(createBtn, false);
    }
  }

  /**
   * Create task in database using MCP tools
   * Reason for Function: Saves task data to Supabase database with proper sub-tasks
   * Task Performed: Inserts new task record and creates associated sub-tasks
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async createTask(taskData) {
    const client = window.getSupabaseClient();
    if (!client) throw new Error('Database client not available');

    // First, create the main task
    const { data: taskResult, error: taskError } = await client
      .from('tasks')
      .insert([{
        title: taskData.title,
        description: taskData.description,
        status: taskData.status,
        priority: taskData.priority,
        due_date: taskData.due_date,
        user_id: taskData.user_id,
        estimated_hours: null,
        actual_hours: null,
        project_id: null,
        task_list_id: null,
        position: 0
      }])
      .select()
      .single();

    if (taskError) throw taskError;

    // Then, create the sub-tasks
    if (taskData.sub_tasks && taskData.sub_tasks.length > 0) {
      const subTasksData = taskData.sub_tasks.map((subTask, index) => ({
        title: subTask,
        description: null,
        is_completed: false,
        position: index,
        task_id: taskResult.id,
        user_id: taskData.user_id
      }));

      const { error: subTaskError } = await client
        .from('sub_tasks')
        .insert(subTasksData);

      if (subTaskError) {
        console.error('Error creating sub-tasks:', subTaskError);
        // Don't throw error here, main task was created successfully
      }
    }

    return taskResult;
  }

  /**
   * Load tasks from database using MCP tools
   * Reason for Function: Retrieves and displays all user tasks with sub-tasks organized by status
   * Task Performed: Fetches tasks and sub-tasks, organizes by status, updates UI
   * Linking Information:
   *   - Internal Link: Uses Supabase MCP tools for database operations
   */
  async loadTasks() {
    try {
      const authManager = window.getAuthManager();
      const currentUser = authManager?.getCurrentUser();

      if (!currentUser) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Load tasks
      const { data: tasksData, error: tasksError } = await client
        .from('tasks')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('created_at', { ascending: false });

      if (tasksError) throw tasksError;

      // Load sub-tasks for all tasks
      const { data: subTasksData, error: subTasksError } = await client
        .from('sub_tasks')
        .select('*')
        .eq('user_id', currentUser.id)
        .order('position', { ascending: true });

      if (subTasksError) throw subTasksError;

      // Group sub-tasks by task_id
      const subTasksByTaskId = {};
      subTasksData?.forEach(subTask => {
        if (!subTasksByTaskId[subTask.task_id]) {
          subTasksByTaskId[subTask.task_id] = [];
        }
        subTasksByTaskId[subTask.task_id].push(subTask);
      });

      // Attach sub-tasks to tasks
      this.tasks = (tasksData || []).map(task => ({
        ...task,
        sub_tasks: subTasksByTaskId[task.id] || []
      }));

      this.renderTasks();
      this.updateTaskCounts();

    } catch (error) {
      console.error('Error loading tasks:', error);
    }
  }

  /**
   * Render tasks in columns
   * Reason for Function: Displays tasks in appropriate status columns
   * Task Performed: Creates task cards and places them in correct columns
   * Linking Information:
   *   - Internal Link: Updates task list elements in tasks.html
   */
  renderTasks() {
    const newTasksList = document.getElementById('new-tasks-list');
    const progressTasksList = document.getElementById('progress-tasks-list');
    const completedTasksList = document.getElementById('completed-tasks-list');

    if (!newTasksList || !progressTasksList || !completedTasksList) return;

    // Clear existing tasks
    newTasksList.innerHTML = '';
    progressTasksList.innerHTML = '';
    completedTasksList.innerHTML = '';

    // Group tasks by status
    const tasksByStatus = {
      new: this.tasks.filter(task => task.status === 'new'),
      in_progress: this.tasks.filter(task => task.status === 'in_progress'),
      completed: this.tasks.filter(task => task.status === 'completed')
    };

    // Render tasks in each column
    this.renderTasksInColumn(tasksByStatus.new, newTasksList, 'new');
    this.renderTasksInColumn(tasksByStatus.in_progress, progressTasksList, 'in_progress');
    this.renderTasksInColumn(tasksByStatus.completed, completedTasksList, 'completed');
  }

  /**
   * Render tasks in specific column
   * Reason for Function: Creates task cards for a specific status column
   * Task Performed: Generates HTML for task cards with checkboxes and sub-tasks
   * Linking Information:
   *   - Internal Link: Called by renderTasks for each status column
   */
  renderTasksInColumn(tasks, container, status) {
    tasks.forEach(task => {
      const taskCard = this.createTaskCard(task, status);
      container.appendChild(taskCard);
    });
  }

  /**
   * Create task card element using database sub-tasks
   * Reason for Function: Generates HTML element for individual task display with database sub-tasks
   * Task Performed: Creates task card with title, checkbox, priority, and sub-tasks from database
   * Linking Information:
   *   - Internal Link: Used by renderTasksInColumn to create task displays
   */
  createTaskCard(task, status) {
    const card = document.createElement('div');
    card.className = 'task-card';
    card.dataset.taskId = task.id;

    const dueDate = task.due_date ? new Date(task.due_date).toLocaleDateString() : '';
    const subTasks = task.sub_tasks || [];

    card.innerHTML = `
      <div class="task-card-header">
        <h4 class="task-title">${task.title}</h4>
        <div class="task-actions">
          <div class="task-checkbox ${status === 'completed' ? 'checked' : ''}"
               onclick="taskManager.toggleTaskStatus('${task.id}', '${status}')">
          </div>
          <button class="delete-task-btn" onclick="taskManager.deleteTask('${task.id}')" title="Delete Task">
            🗑️
          </button>
        </div>
      </div>
      <div class="task-meta">
        <span class="task-priority ${task.priority}">${task.priority}</span>
        ${dueDate ? `<span>📅 ${dueDate}</span>` : ''}
      </div>
      ${subTasks.length > 0 ? `
        <div class="sub-tasks-list">
          ${subTasks.map((subTask) => `
            <div class="sub-task-item-display">
              <div class="sub-task-checkbox ${subTask.is_completed ? 'checked' : ''}"
                   onclick="taskManager.toggleSubTask('${subTask.id}')">
              </div>
              <span class="sub-task-text">${subTask.title}</span>
              <button class="delete-sub-task-btn" onclick="taskManager.deleteSubTask('${subTask.id}')" title="Delete Sub-task">
                🗑️
              </button>
            </div>
          `).join('')}
        </div>
      ` : ''}
    `;

    return card;
  }

  /**
   * Toggle task status using MCP tools
   * Reason for Function: Moves task between status columns when checkbox is clicked
   * Task Performed: Updates task status in database using MCP tools and refreshes display
   * Linking Information:
   *   - Internal Link: Called by task checkbox click events
   */
  async toggleTaskStatus(taskId, currentStatus) {
    try {
      let newStatus;
      if (currentStatus === 'new') {
        newStatus = 'in_progress';
      } else if (currentStatus === 'in_progress') {
        newStatus = 'completed';
      } else {
        return; // Can't change completed tasks
      }

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      const updateData = {
        status: newStatus,
        updated_at: new Date().toISOString()
      };

      if (newStatus === 'completed') {
        updateData.completed_at = new Date().toISOString();
      }

      const { error } = await client
        .from('tasks')
        .update(updateData)
        .eq('id', taskId);

      if (error) throw error;

      // Reload tasks to update display
      this.loadTasks();

    } catch (error) {
      console.error('Error updating task status:', error);
      alert('Failed to update task status. Please try again.');
    }
  }

  /**
   * Toggle sub-task completion using MCP tools
   * Reason for Function: Marks individual sub-tasks as completed or incomplete in database
   * Task Performed: Updates sub-task completion status in database using MCP tools
   * Linking Information:
   *   - Internal Link: Called by sub-task checkbox click events
   */
  async toggleSubTask(subTaskId) {
    try {
      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // First, get the current sub-task to toggle its status
      const { data: subTask, error: fetchError } = await client
        .from('sub_tasks')
        .select('is_completed')
        .eq('id', subTaskId)
        .single();

      if (fetchError) throw fetchError;

      // Toggle the completion status
      const newStatus = !subTask.is_completed;

      const { error: updateError } = await client
        .from('sub_tasks')
        .update({
          is_completed: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', subTaskId);

      if (updateError) throw updateError;

      // Reload tasks to update display
      this.loadTasks();

    } catch (error) {
      console.error('Error updating sub-task status:', error);
      alert('Failed to update sub-task status. Please try again.');
    }
  }

  /**
   * Update task counts
   * Reason for Function: Updates the count badges in column headers
   * Task Performed: Counts tasks by status and updates display numbers
   * Linking Information:
   *   - Internal Link: Updates count elements in task column headers
   */
  updateTaskCounts() {
    const newCount = this.tasks.filter(task => task.status === 'new').length;
    const progressCount = this.tasks.filter(task => task.status === 'in_progress').length;
    const completedCount = this.tasks.filter(task => task.status === 'completed').length;

    const newCountEl = document.getElementById('new-task-count');
    const progressCountEl = document.getElementById('progress-task-count');
    const completedCountEl = document.getElementById('completed-task-count');

    if (newCountEl) newCountEl.textContent = newCount;
    if (progressCountEl) progressCountEl.textContent = progressCount;
    if (completedCountEl) completedCountEl.textContent = completedCount;
  }

  /**
   * Delete task and all its sub-tasks
   * Reason for Function: Removes task and associated sub-tasks from database
   * Task Performed: Confirms deletion, removes from database using MCP tools
   * Linking Information:
   *   - Internal Link: Called by delete task button click events
   */
  async deleteTask(taskId) {
    try {
      // Confirm deletion
      const confirmed = confirm('Are you sure you want to delete this task and all its sub-tasks? This action cannot be undone.');
      if (!confirmed) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Delete the main task (sub-tasks will be deleted automatically due to CASCADE)
      const { error } = await client
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      // Reload tasks to update display
      this.loadTasks();

    } catch (error) {
      console.error('Error deleting task:', error);
      alert('Failed to delete task. Please try again.');
    }
  }

  /**
   * Delete individual sub-task
   * Reason for Function: Removes individual sub-task from database
   * Task Performed: Confirms deletion, removes sub-task from database using MCP tools
   * Linking Information:
   *   - Internal Link: Called by delete sub-task button click events
   */
  async deleteSubTask(subTaskId) {
    try {
      // Confirm deletion
      const confirmed = confirm('Are you sure you want to delete this sub-task? This action cannot be undone.');
      if (!confirmed) return;

      const client = window.getSupabaseClient();
      if (!client) throw new Error('Database client not available');

      // Delete the sub-task
      const { error } = await client
        .from('sub_tasks')
        .delete()
        .eq('id', subTaskId);

      if (error) throw error;

      // Reload tasks to update display
      this.loadTasks();

    } catch (error) {
      console.error('Error deleting sub-task:', error);
      alert('Failed to delete sub-task. Please try again.');
    }
  }

  /**
   * Set loading state for buttons
   * Reason for Function: Shows loading state during async operations
   * Task Performed: Adds/removes loading class and disables button
   * Linking Information:
   *   - Internal Link: Used during task creation and updates
   */
  setLoading(button, loading) {
    if (loading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }
}

// Export for global use
window.TaskManager = TaskManager;
