<!-- Tasks Page Content -->
<div class="tasks-page">
    <div class="page-header">
        <h2>Task Management</h2>
        <button class="btn btn-primary" id="add-task-btn">
            <span class="icon">➕</span>
            Add New Task
        </button>
    </div>

    <!-- Task Columns -->
    <div class="task-columns">
        <div class="task-column">
            <div class="column-header">
                <h3>New Tasks</h3>
                <span class="task-count" id="new-task-count">0</span>
            </div>
            <div class="task-list" id="new-tasks-list">
                <!-- New tasks will be loaded here -->
            </div>
        </div>

        <div class="task-column">
            <div class="column-header">
                <h3>In Progress</h3>
                <span class="task-count" id="progress-task-count">0</span>
            </div>
            <div class="task-list" id="progress-tasks-list">
                <!-- In progress tasks will be loaded here -->
            </div>
        </div>

        <div class="task-column">
            <div class="column-header">
                <h3>Completed</h3>
                <span class="task-count" id="completed-task-count">0</span>
            </div>
            <div class="task-list" id="completed-tasks-list">
                <!-- Completed tasks will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Add Task Modal -->
<div id="add-task-modal" class="modal hidden">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add New Task</h3>
            <button class="modal-close" id="close-task-modal">×</button>
        </div>
        <div class="modal-body">
            <form id="add-task-form">
                <div class="form-group">
                    <label for="main-task-title">Main Task Title</label>
                    <input type="text" id="main-task-title" required placeholder="Enter main task title">
                </div>

                <div class="form-group">
                    <label>Sub Tasks</label>
                    <div id="sub-tasks-container">
                        <div class="sub-task-item">
                            <input type="text" class="sub-task-input" placeholder="Enter sub task" required>
                            <button type="button" class="remove-sub-task" disabled>🗑️</button>
                        </div>
                    </div>
                    <button type="button" id="add-sub-task" class="btn btn-secondary">
                        <span class="icon">➕</span>
                        Add Sub Task
                    </button>
                </div>

                <div class="form-group">
                    <label for="task-priority">Priority</label>
                    <select id="task-priority">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="task-due-date">Due Date (Optional)</label>
                    <input type="date" id="task-due-date">
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancel-task">Cancel</button>
            <button type="submit" form="add-task-form" class="btn btn-primary" id="create-task">Create Task</button>
        </div>
    </div>
</div>
