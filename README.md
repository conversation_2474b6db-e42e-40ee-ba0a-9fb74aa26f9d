# Task Pro Desktop Application

A comprehensive desktop task management application built with Electron.js and Supabase, designed for managing daily tasks, clients, projects, payments, and invoice generation.

## Features

### ✅ Core Functionality
- **Authentication System**: Secure login/signup with Supabase authentication
- **Dashboard**: Overview of tasks, projects, and quick actions
- **Task Management**: Create, organize, and track tasks (Coming Soon)
- **Client Management**: Manage client information and relationships (Coming Soon)
- **Project Management**: Organize and track project progress (Coming Soon)
- **Payment Tracking**: Monitor payments and financial data (Coming Soon)
- **Invoice Generation**: Create and manage professional invoices (Coming Soon)

### 🎨 User Interface
- **Modern Design**: Clean, professional interface with smooth animations
- **Dark/Light Theme**: Toggle between light and dark modes
- **Responsive Layout**: Optimized for different screen sizes
- **Collapsible Sidebar**: Space-efficient navigation with icon-only mode
- **Real-time Updates**: Live data synchronization with Supabase

### 🔧 Technical Features
- **Cache Management**: Automatic cache clearing on startup for fresh content
- **Offline Support**: Core functionality available without internet connection
- **Security**: Context isolation and secure communication between processes
- **Cross-platform**: Built with Electron for Windows, macOS, and Linux support

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Desktop Framework**: Electron.js
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Styling**: Custom CSS with CSS Variables
- **Build Tool**: Electron Builder

## Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager
- Windows 10/11 (for Windows executable)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd task-pro-desktop
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Supabase** (Already configured for Task Pro project)
   - The application is pre-configured to connect to the existing "Task Pro" Supabase project
   - Database URL: `https://mgrfytojvuywenlnefpd.supabase.co`

## Development

### Running in Development Mode
```bash
npm run dev
```
This starts the application in development mode with DevTools enabled.

### Running in Production Mode
```bash
npm start
```

## Building for Production

### Build Windows Executable
```bash
npm run build:win
```
This creates a Windows installer (.exe) in the `dist` folder.

### Build for All Platforms
```bash
npm run build
```

## Project Structure

```
task-pro-desktop/
├── src/
│   ├── main.js                 # Main Electron process
│   ├── preload.js             # Preload script for security
│   └── renderer/              # Renderer process files
│       ├── index.html         # Main HTML file
│       ├── js/                # JavaScript modules
│       │   ├── app.js         # Main application logic
│       │   ├── auth.js        # Authentication management
│       │   ├── navigation.js  # Page routing and navigation
│       │   ├── theme.js       # Theme management
│       │   └── supabase-client.js # Supabase integration
│       ├── styles/            # CSS stylesheets
│       │   ├── main.css       # Global styles and variables
│       │   ├── auth.css       # Authentication styles
│       │   ├── dashboard.css  # Dashboard layout styles
│       │   └── components.css # Component styles
│       └── pages/             # Page templates
│           ├── dashboard.html
│           ├── tasks.html
│           ├── clients.html
│           ├── projects.html
│           ├── payments.html
│           ├── invoice.html
│           ├── profile.html
│           └── settings.html
├── public/                    # Static assets
│   ├── icon.png              # Application icon
│   └── icon.ico              # Windows icon
├── package.json              # Project configuration
└── README.md                 # This file
```

## Database Schema

The application connects to a Supabase database with the following tables:

- **profiles**: User profile information
- **tasks**: Task management data
- **projects**: Project information
- **task_lists**: Daily task lists
- **task_analytics**: Task completion analytics

## Usage

1. **Launch the Application**
   - Run the executable or use `npm start`
   - The application will open with a login screen

2. **Authentication**
   - Sign up for a new account or log in with existing credentials
   - Email verification may be required for new accounts

3. **Navigation**
   - Use the sidebar to navigate between different sections
   - Click the hamburger menu to collapse/expand the sidebar
   - Use Ctrl+B to toggle sidebar visibility

4. **Theme Switching**
   - Click the theme toggle button in the header to switch between light/dark modes
   - Theme preference is automatically saved

## Keyboard Shortcuts

- `Ctrl + B`: Toggle sidebar
- `Ctrl + R` / `F5`: Refresh application (prevented to avoid cache issues)

## Cache Management

The application automatically clears cache on startup to ensure fresh content display. This includes:
- Browser cache
- Local storage (except user preferences)
- Session storage

## Security Features

- Context isolation between main and renderer processes
- Secure communication via preload scripts
- No direct Node.js access in renderer
- Content Security Policy (CSP) implementation
- Secure Supabase connection with environment variables

## Troubleshooting

### Application Won't Start
- Ensure Node.js is installed and up to date
- Run `npm install` to reinstall dependencies
- Check console for error messages

### Authentication Issues
- Verify internet connection
- Check Supabase project status
- Clear application data and try again

### Build Issues
- Ensure all dependencies are installed
- Check that the build directory has write permissions
- Verify Electron Builder configuration

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Task Pro Desktop** - Streamline your productivity with professional task management.
