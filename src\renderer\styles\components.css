/**
 * Component styles for reusable UI elements
 * Reason for Function: Provides styling for reusable components like cards, buttons, forms, and page-specific elements
 * Task Performed: Styles dashboard components, coming soon sections, stats cards, and other UI elements
 * Linking Information:
 *   - Internal Link: Used by src/renderer/pages/*.html files for component styling
 *   - Internal Link: Styles referenced throughout the application for consistent UI
 */

/* Page Header */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
}

.stat-content h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
}

.dashboard-section {
  background-color: var(--bg-primary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.dashboard-section h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

/* Quick Actions */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateX(4px);
}

.action-icon {
  font-size: 1.25rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border-radius: var(--radius-md);
}

/* Coming Soon Components */
.coming-soon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.coming-soon-content {
  text-align: center;
  max-width: 500px;
  padding: var(--spacing-xl);
}

.coming-soon-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.8;
}

.coming-soon-content h3 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.coming-soon-content p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
}

/* Features Preview */
.features-preview {
  background-color: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.features-preview ul {
  list-style: none;
  text-align: left;
}

.features-preview li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.features-preview li:not(:last-child) {
  border-bottom: 1px solid var(--border-color);
}

/* Task List */
.task-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.task-item {
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-fast);
}

.task-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: between;
  margin-bottom: var(--spacing-sm);
}

.task-title {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.task-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.task-status.new {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.task-status.in-progress {
  background-color: #fef3c7;
  color: #d97706;
}

.task-status.completed {
  background-color: #d1fae5;
  color: #059669;
}

.task-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Cards */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-content {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Task Management Styles */
.task-columns {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.task-column {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  min-height: 500px;
}

.column-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.column-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.task-count {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.task-list {
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 450px;
  overflow-y: auto;
}

.task-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.task-card:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.task-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.task-title {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
}

.task-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.task-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
}

.task-checkbox:hover {
  border-color: var(--primary-color);
}

.task-checkbox.checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);
}

.task-checkbox.checked::after {
  content: '✓';
  font-size: 12px;
  font-weight: bold;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.task-priority {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.task-priority.low {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.task-priority.medium {
  background-color: #fef3c7;
  color: #d97706;
}

.task-priority.high {
  background-color: #fee2e2;
  color: #dc2626;
}

.sub-tasks-list {
  margin-top: var(--spacing-sm);
  padding-left: var(--spacing-md);
}

.sub-task-item-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.sub-task-text {
  flex: 1;
}

.sub-task-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary);
}

.sub-task-checkbox:hover {
  border-color: var(--primary-color);
}

.sub-task-checkbox.checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--text-inverse);
}

.sub-task-checkbox.checked::after {
  content: '✓';
  font-size: 10px;
  font-weight: bold;
}

/* Delete Button Styles */
.delete-task-btn,
.delete-sub-task-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  opacity: 0.7;
}

.delete-task-btn:hover,
.delete-sub-task-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  opacity: 1;
  transform: scale(1.1);
}

.delete-task-btn {
  font-size: 1rem;
}

.delete-sub-task-btn {
  font-size: 0.75rem;
  padding: 2px;
}

/* Hide delete buttons by default, show on hover */
.task-card .delete-task-btn,
.sub-task-item-display .delete-sub-task-btn {
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
}

.task-card:hover .delete-task-btn,
.sub-task-item-display:hover .delete-sub-task-btn {
  opacity: 0.7;
  visibility: visible;
}

.task-card:hover .delete-task-btn:hover,
.sub-task-item-display:hover .delete-sub-task-btn:hover {
  opacity: 1;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  margin: var(--spacing-md);
  animation: modalSlide 0.3s ease-out;
}

@keyframes modalSlide {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* Sub Task Form Styles */
.sub-task-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.sub-task-input {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
}

.remove-sub-task {
  background: none;
  border: none;
  color: var(--danger-color);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  font-size: 1rem;
}

.remove-sub-task:hover:not(:disabled) {
  background-color: rgba(239, 68, 68, 0.1);
}

.remove-sub-task:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form Select Styles */
select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .task-columns {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .modal-content {
    margin: var(--spacing-sm);
    max-width: none;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .coming-soon-content {
    padding: var(--spacing-lg);
  }

  .coming-soon-icon {
    font-size: 3rem;
  }

  .coming-soon-content h3 {
    font-size: 1.5rem;
  }

  .coming-soon-content p {
    font-size: 1rem;
  }
}
