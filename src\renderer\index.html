<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co;">
    <title>Task Pro - Desktop</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/auth.css">
    <link rel="stylesheet" href="styles/dashboard.css">
</head>
<body>
    <!-- Main Application Container -->
    <div id="app">
        <!-- Loading Screen -->
        <div id="loading-screen" class="loading-screen">
            <div class="loading-spinner"></div>
            <h2>Task Pro</h2>
            <p>Loading your workspace...</p>
        </div>

        <!-- Authentication Container -->
        <div id="auth-container" class="auth-container hidden">
            <!-- Login Form -->
            <div id="login-form" class="auth-form">
                <div class="auth-header">
                    <h1>Welcome Back</h1>
                    <p>Sign in to your Task Pro account</p>
                </div>
                <form id="login-form-element">
                    <div class="form-group">
                        <label for="login-email">Email</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">Password</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Sign In</button>
                </form>
                <div class="auth-footer">
                    <p>Don't have an account? <a href="#" id="show-signup">Sign up</a></p>
                </div>
            </div>

            <!-- Signup Form -->
            <div id="signup-form" class="auth-form hidden">
                <div class="auth-header">
                    <h1>Create Account</h1>
                    <p>Join Task Pro to manage your tasks</p>
                </div>
                <form id="signup-form-element">
                    <div class="form-group">
                        <label for="signup-email">Email</label>
                        <input type="email" id="signup-email" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-password">Password</label>
                        <input type="password" id="signup-password" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-confirm-password">Confirm Password</label>
                        <input type="password" id="signup-confirm-password" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-full-name">Full Name</label>
                        <input type="text" id="signup-full-name" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Create Account</button>
                </form>
                <div class="auth-footer">
                    <p>Already have an account? <a href="#" id="show-login">Sign in</a></p>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Container -->
        <div id="dashboard-container" class="dashboard-container hidden">
            <!-- Sidebar -->
            <aside id="sidebar" class="sidebar">
                <div class="sidebar-header">
                    <div class="logo">
                        <span class="logo-icon">📋</span>
                        <span class="logo-text">Task Pro</span>
                    </div>
                    <button id="sidebar-toggle" class="sidebar-toggle">
                        <span class="hamburger"></span>
                    </button>
                </div>
                
                <nav class="sidebar-nav">
                    <ul class="nav-list">
                        <li class="nav-item active" data-page="dashboard">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="tasks">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">✅</span>
                                <span class="nav-text">Tasks</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="clients">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">👥</span>
                                <span class="nav-text">Clients</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="projects">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">📁</span>
                                <span class="nav-text">Projects</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="payments">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">💳</span>
                                <span class="nav-text">Payments</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="invoice">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">📄</span>
                                <span class="nav-text">Invoice</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="profile">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">👤</span>
                                <span class="nav-text">Profile</span>
                            </a>
                        </li>
                        <li class="nav-item" data-page="settings">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">Settings</span>
                            </a>
                        </li>
                    </ul>
                </nav>

                <div class="sidebar-footer">
                    <button id="logout-btn" class="logout-btn">
                        <span class="nav-icon">🚪</span>
                        <span class="nav-text">Logout</span>
                    </button>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Header -->
                <header class="header">
                    <div class="header-left">
                        <h1 id="page-title">Dashboard</h1>
                    </div>
                    <div class="header-right">
                        <div class="search-container">
                            <input type="text" id="search-input" placeholder="Search...">
                            <span class="search-icon">🔍</span>
                        </div>
                        <button id="notifications-btn" class="header-btn">
                            <span class="icon">🔔</span>
                        </button>
                        <button id="theme-toggle" class="header-btn">
                            <span class="icon">🌙</span>
                        </button>
                        <div class="profile-menu">
                            <button id="profile-btn" class="profile-btn">
                                <span class="profile-avatar">👤</span>
                            </button>
                            <div id="profile-dropdown" class="profile-dropdown hidden">
                                <div class="dropdown-item" id="goto-profile">
                                    <span class="dropdown-icon">👤</span>
                                    <span>Profile</span>
                                </div>
                                <div class="dropdown-divider"></div>
                                <div class="dropdown-item" id="dropdown-logout">
                                    <span class="dropdown-icon">🚪</span>
                                    <span>Logout</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Page Content -->
                <div id="page-content" class="page-content">
                    <!-- Dynamic content will be loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/supabase-bundle.js"></script>
    <script src="js/supabase-client.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/task-manager.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/theme.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
