/**
 * Dashboard layout and component styles
 * Reason for Function: Provides styling for the main dashboard layout including sidebar, header, and content areas
 * Task Performed: Styles the dashboard container, sidebar navigation, header, and page content areas
 * Linking Information:
 *   - Internal Link: Used by src/renderer/index.html for dashboard layout
 *   - Internal Link: Styles referenced by src/renderer/js/navigation.js for sidebar interactions
 */

/* Dashboard Container */
.dashboard-container {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-sidebar);
  color: var(--text-inverse);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-normal);
  z-index: 100;
  border-right: 1px solid var(--border-color);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: var(--header-height);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.25rem;
  font-weight: 700;
}

.logo-icon {
  font-size: 1.5rem;
}

.sidebar.collapsed .logo-text {
  display: none;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-inverse);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger {
  display: block;
  width: 20px;
  height: 2px;
  background-color: currentColor;
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background-color: currentColor;
  transition: transform var(--transition-fast);
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  top: 6px;
}

/* Sidebar Navigation */
.sidebar-nav {
  flex: 1;
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-inverse);
}

.nav-item.active .nav-link {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.nav-item.active .nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--accent-color);
}

.nav-icon {
  font-size: 1.25rem;
  min-width: 24px;
  text-align: center;
}

.sidebar.collapsed .nav-text {
  display: none;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
  padding: var(--spacing-md);
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
  padding: var(--spacing-md);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
}

.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.sidebar.collapsed .logout-btn .nav-text {
  display: none;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Header */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  background-color: var(--bg-header);
  border-bottom: 1px solid var(--border-color);
  height: var(--header-height);
  box-shadow: var(--shadow-sm);
}

.header-left h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Search Container */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-container input {
  width: 300px;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.search-container input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.search-icon {
  position: absolute;
  right: var(--spacing-md);
  color: var(--text-secondary);
  pointer-events: none;
}

/* Header Buttons */
.header-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.header-btn:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.header-btn .icon {
  font-size: 1.25rem;
}

/* Profile Menu */
.profile-menu {
  position: relative;
}

.profile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  border: none;
  border-radius: 50%;
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.profile-btn:hover {
  background-color: var(--primary-hover);
}

.profile-avatar {
  font-size: 1.25rem;
}

/* Profile Dropdown */
.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-sm);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 180px;
  z-index: 1000;
  animation: dropdownSlide 0.2s ease-out;
}

@keyframes dropdownSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  color: var(--text-primary);
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
}

.dropdown-item:first-child {
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.dropdown-item:last-child {
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.dropdown-icon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* Page Content */
.page-content {
  flex: 1;
  padding: var(--spacing-xl);
  background-color: var(--bg-secondary);
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-container input {
    width: 250px;
  }

  .header-right {
    gap: var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -100%;
    z-index: 1000;
    transition: left var(--transition-normal);
    width: var(--sidebar-width);
  }

  .sidebar.open {
    left: 0;
  }

  .sidebar.collapsed {
    width: var(--sidebar-width);
  }

  .search-container input {
    width: 180px;
  }

  .header {
    padding: 0 var(--spacing-md);
  }

  .header-left h1 {
    font-size: 1.25rem;
  }

  .page-content {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .search-container {
    display: none;
  }

  .header {
    padding: 0 var(--spacing-sm);
  }

  .header-right {
    gap: var(--spacing-xs);
  }

  .header-btn {
    width: 36px;
    height: 36px;
  }

  .profile-btn {
    width: 36px;
    height: 36px;
  }
}
