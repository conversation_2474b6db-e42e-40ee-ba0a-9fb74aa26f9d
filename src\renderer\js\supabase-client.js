/**
 * Supabase client configuration and initialization
 * Reason for Function: Initializes and configures the Supabase client for database operations and authentication
 * Task Performed: Sets up Supabase connection, provides database access methods, and handles authentication state
 * Linking Information:
 *   - Internal Link: Used by src/renderer/js/auth.js for authentication operations
 *   - Internal Link: Used by all page scripts for database operations
 *   - External Link: Connects to Supabase project at https://mgrfytojvuywenlnefpd.supabase.co
 */

// Supabase configuration
const SUPABASE_URL = 'https://mgrfytojvuywenlnefpd.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1ncmZ5dG9qdnV5d2VubG5lZnBkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM1ODkzMjgsImV4cCI6MjA2OTE2NTMyOH0.KtG-X1uM55dCsQszpCxgwz8Ydc9AF4dj9KgnbvtRnZw';

// Initialize Supabase client
let supabase;

/**
 * Initialize Supabase client
 * Reason for Function: Creates and configures the Supabase client instance with proper settings
 * Task Performed: Initializes the Supabase client with URL and API key, sets up authentication persistence
 * Linking Information:
 *   - External Link: Uses @supabase/supabase-js library
 *   - Internal Link: Client instance used throughout the application
 */
function initializeSupabase() {
  try {
    // Check if Supabase createClient is available
    let createClient;

    if (typeof window.supabaseCreateClient !== 'undefined') {
      // Use bundled version
      createClient = window.supabaseCreateClient;
    } else if (typeof window.supabase !== 'undefined') {
      // Use CDN version as fallback
      createClient = window.supabase.createClient;
    } else {
      console.error('Supabase library not available');
      return null;
    }

    supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false
      },
      db: {
        schema: 'public'
      }
    });

    console.log('Supabase client initialized successfully');
    return supabase;
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    return null;
  }
}

/**
 * Get current Supabase client instance
 * Reason for Function: Provides access to the initialized Supabase client for other modules
 * Task Performed: Returns the current Supabase client instance or initializes it if not available
 * Linking Information:
 *   - Internal Link: Used by all modules that need database access
 */
function getSupabaseClient() {
  if (!supabase) {
    supabase = initializeSupabase();
  }
  return supabase;
}

/**
 * Authentication helper functions
 * Reason for Function: Provides convenient methods for authentication operations
 * Task Performed: Wraps Supabase auth methods with error handling and logging
 * Linking Information:
 *   - Internal Link: Used by src/renderer/js/auth.js for user authentication
 */
const SupabaseAuth = {
  /**
   * Sign up a new user
   */
  async signUp(email, password, userData = {}) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Sign up error:', error);
      return { data: null, error };
    }
  },

  /**
   * Sign in an existing user
   */
  async signIn(email, password) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Sign in error:', error);
      return { data: null, error };
    }
  },

  /**
   * Sign out current user
   */
  async signOut() {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { error } = await client.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      console.error('Sign out error:', error);
      return { error };
    }
  },

  /**
   * Get current user session
   */
  async getSession() {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data: { session }, error } = await client.auth.getSession();
      if (error) throw error;
      return { session, error: null };
    } catch (error) {
      console.error('Get session error:', error);
      return { session: null, error };
    }
  },

  /**
   * Get current user
   */
  async getUser() {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data: { user }, error } = await client.auth.getUser();
      if (error) throw error;
      return { user, error: null };
    } catch (error) {
      console.error('Get user error:', error);
      return { user: null, error };
    }
  },

  /**
   * Listen to authentication state changes
   */
  onAuthStateChange(callback) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      return client.auth.onAuthStateChange(callback);
    } catch (error) {
      console.error('Auth state change listener error:', error);
      return null;
    }
  }
};

/**
 * Database helper functions
 * Reason for Function: Provides convenient methods for database operations
 * Task Performed: Wraps Supabase database methods with error handling and logging
 * Linking Information:
 *   - Internal Link: Used by page scripts for data operations
 */
const SupabaseDB = {
  /**
   * Get tasks for current user with sub-tasks
   */
  async getTasks(userId) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      // Get tasks
      const { data: tasksData, error: tasksError } = await client
        .from('tasks')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (tasksError) throw tasksError;

      // Get sub-tasks
      const { data: subTasksData, error: subTasksError } = await client
        .from('sub_tasks')
        .select('*')
        .eq('user_id', userId)
        .order('position', { ascending: true });

      if (subTasksError) throw subTasksError;

      // Group sub-tasks by task_id
      const subTasksByTaskId = {};
      subTasksData?.forEach(subTask => {
        if (!subTasksByTaskId[subTask.task_id]) {
          subTasksByTaskId[subTask.task_id] = [];
        }
        subTasksByTaskId[subTask.task_id].push(subTask);
      });

      // Attach sub-tasks to tasks
      const tasksWithSubTasks = (tasksData || []).map(task => ({
        ...task,
        sub_tasks: subTasksByTaskId[task.id] || []
      }));

      return { data: tasksWithSubTasks, error: null };
    } catch (error) {
      console.error('Get tasks error:', error);
      return { data: null, error };
    }
  },

  /**
   * Create a new task with sub-tasks
   */
  async createTask(taskData) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      // Create main task
      const { data: taskResult, error: taskError } = await client
        .from('tasks')
        .insert([taskData])
        .select()
        .single();

      if (taskError) throw taskError;

      return { data: taskResult, error: null };
    } catch (error) {
      console.error('Create task error:', error);
      return { data: null, error };
    }
  },

  /**
   * Create sub-tasks for a task
   */
  async createSubTasks(subTasksData) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('sub_tasks')
        .insert(subTasksData)
        .select();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Create sub-tasks error:', error);
      return { data: null, error };
    }
  },

  /**
   * Update task status
   */
  async updateTaskStatus(taskId, status, completedAt = null) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const updateData = {
        status,
        updated_at: new Date().toISOString()
      };

      if (completedAt) {
        updateData.completed_at = completedAt;
      }

      const { data, error } = await client
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Update task status error:', error);
      return { data: null, error };
    }
  },

  /**
   * Update sub-task completion status
   */
  async updateSubTaskStatus(subTaskId, isCompleted) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('sub_tasks')
        .update({
          is_completed: isCompleted,
          updated_at: new Date().toISOString()
        })
        .eq('id', subTaskId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Update sub-task status error:', error);
      return { data: null, error };
    }
  },

  /**
   * Delete a task and all its sub-tasks
   */
  async deleteTask(taskId) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .select();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Delete task error:', error);
      return { data: null, error };
    }
  },

  /**
   * Delete a sub-task
   */
  async deleteSubTask(subTaskId) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('sub_tasks')
        .delete()
        .eq('id', subTaskId)
        .select();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Delete sub-task error:', error);
      return { data: null, error };
    }
  },

  /**
   * Get projects for current user
   */
  async getProjects(userId) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('projects')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Get projects error:', error);
      return { data: null, error };
    }
  },

  /**
   * Get user profile
   */
  async getProfile(userId) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Get profile error:', error);
      return { data: null, error };
    }
  },

  /**
   * Update user profile
   */
  async updateProfile(userId, updates) {
    try {
      const client = getSupabaseClient();
      if (!client) throw new Error('Supabase client not available');

      const { data, error } = await client
        .from('profiles')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Update profile error:', error);
      return { data: null, error };
    }
  }
};

// Initialize Supabase when script loads
document.addEventListener('DOMContentLoaded', () => {
  initializeSupabase();
});

// Export for use in other modules
window.SupabaseAuth = SupabaseAuth;
window.SupabaseDB = SupabaseDB;
window.getSupabaseClient = getSupabaseClient;
